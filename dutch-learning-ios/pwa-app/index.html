<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Dutch Learning - Clozemaster <PERSON></title>
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#2196F3">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Dutch Learning">
    <link rel="apple-touch-icon" href="icon-192.png">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 100vw;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            font-size: 14px;
        }
        
        .difficulty-selector {
            display: flex;
            background: white;
            margin: 15px;
            border-radius: 10px;
            padding: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .difficulty-btn {
            flex: 1;
            padding: 12px;
            text-align: center;
            border: none;
            background: transparent;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .difficulty-btn.active {
            background: #2196F3;
            color: white;
            font-weight: bold;
        }
        
        .exercise-card {
            background: white;
            margin: 15px;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            flex: 1;
        }
        
        .sentence-container {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .dutch-sentence {
            font-size: 20px;
            font-weight: bold;
            flex: 1;
            line-height: 1.4;
        }
        
        .speaker-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            padding: 10px;
            border-radius: 50%;
            transition: background 0.3s ease;
        }
        
        .speaker-btn:hover {
            background: #f0f0f0;
        }
        
        .english-sentence {
            font-size: 16px;
            color: #666;
            font-style: italic;
            margin-bottom: 25px;
        }
        
        .answer-input {
            width: 100%;
            padding: 15px;
            font-size: 18px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .answer-input:focus {
            outline: none;
            border-color: #2196F3;
            background: white;
        }
        
        .answer-input.correct {
            border-color: #4CAF50;
            background: #f1f8e9;
        }
        
        .answer-input.incorrect {
            border-color: #f44336;
            background: #ffebee;
        }
        
        .feedback {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .feedback-text {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .feedback-text.correct {
            color: #4CAF50;
        }
        
        .feedback-text.incorrect {
            color: #f44336;
        }
        
        .correct-answer {
            font-size: 16px;
            color: #666;
            font-style: italic;
        }
        
        .button-container {
            text-align: center;
        }
        
        .btn {
            padding: 15px 30px;
            font-size: 16px;
            font-weight: bold;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }
        
        .btn-check {
            background: #2196F3;
            color: white;
        }
        
        .btn-next {
            background: #4CAF50;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .footer {
            padding: 20px;
            text-align: center;
        }
        
        .reset-btn {
            background: none;
            border: none;
            color: #f44336;
            cursor: pointer;
            font-size: 14px;
            padding: 10px;
        }
        
        .hidden {
            display: none;
        }
        
        /* iOS specific styles */
        @supports (-webkit-touch-callout: none) {
            .container {
                padding-bottom: env(safe-area-inset-bottom);
            }
            
            .header {
                padding-top: max(20px, env(safe-area-inset-top));
            }
        }
        
        /* Responsive design */
        @media (max-width: 480px) {
            .exercise-card {
                margin: 10px;
                padding: 20px;
            }
            
            .dutch-sentence {
                font-size: 18px;
            }
            
            .answer-input {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">Dutch Learning</div>
            <div class="stats">
                <span>Score: <span id="score">0</span>/<span id="total">0</span></span>
                <span>Accuracy: <span id="accuracy">0</span>%</span>
                <span>Streak: <span id="streak">0</span></span>
            </div>
        </div>
        
        <div class="difficulty-selector">
            <button class="difficulty-btn active" data-difficulty="common">Common</button>
            <button class="difficulty-btn" data-difficulty="intermediate">Intermediate</button>
            <button class="difficulty-btn" data-difficulty="advanced">Advanced</button>
        </div>
        
        <div class="exercise-card">
            <div class="sentence-container">
                <div class="dutch-sentence" id="dutch-sentence">Loading...</div>
                <button class="speaker-btn" id="speaker-btn">🔊</button>
            </div>
            
            <div class="english-sentence" id="english-sentence">Loading...</div>
            
            <input type="text" class="answer-input" id="answer-input" placeholder="Type your answer here..." autocomplete="off" autocapitalize="none" autocorrect="off">
            
            <div class="feedback hidden" id="feedback">
                <div class="feedback-text" id="feedback-text"></div>
                <div class="correct-answer hidden" id="correct-answer"></div>
            </div>
            
            <div class="button-container">
                <button class="btn btn-check" id="check-btn">Check Answer</button>
                <button class="btn btn-next hidden" id="next-btn">Next Sentence</button>
            </div>
        </div>
        
        <div class="footer">
            <button class="reset-btn" id="reset-btn">Reset Progress</button>
        </div>
    </div>

    <script>
        // Dutch sentences data
        const sentences = [
            { id: 1, dutch: "Ik hou van ___.", english: "I love coffee.", answer: "koffie", difficulty: "common", audio: "Ik hou van koffie" },
            { id: 2, dutch: "Het ___ is mooi vandaag.", english: "The weather is beautiful today.", answer: "weer", difficulty: "common", audio: "Het weer is mooi vandaag" },
            { id: 3, dutch: "Waar is de ___?", english: "Where is the bathroom?", answer: "badkamer", difficulty: "common", audio: "Waar is de badkamer" },
            { id: 4, dutch: "Ik wil graag een ___ bestellen.", english: "I would like to order a beer.", answer: "biertje", difficulty: "common", audio: "Ik wil graag een biertje bestellen" },
            { id: 5, dutch: "Hoeveel ___ dit?", english: "How much does this cost?", answer: "kost", difficulty: "common", audio: "Hoeveel kost dit" },
            { id: 6, dutch: "Ik spreek geen ___.", english: "I don't speak Dutch.", answer: "Nederlands", difficulty: "common", audio: "Ik spreek geen Nederlands" },
            { id: 7, dutch: "Kun je me ___?", english: "Can you help me?", answer: "helpen", difficulty: "common", audio: "Kun je me helpen" },
            { id: 8, dutch: "Ik ben ___ aan het leren.", english: "I am learning Dutch.", answer: "Nederlands", difficulty: "common", audio: "Ik ben Nederlands aan het leren" },
            { id: 9, dutch: "De ___ is gesloten.", english: "The store is closed.", answer: "winkel", difficulty: "common", audio: "De winkel is gesloten" },
            { id: 10, dutch: "Wat is je ___?", english: "What is your name?", answer: "naam", difficulty: "common", audio: "Wat is je naam" },
            { id: 11, dutch: "Hij heeft een ___ gekocht.", english: "He bought a bicycle.", answer: "fiets", difficulty: "intermediate", audio: "Hij heeft een fiets gekocht" },
            { id: 12, dutch: "De vergadering is ___.", english: "The meeting is postponed.", answer: "uitgesteld", difficulty: "intermediate", audio: "De vergadering is uitgesteld" },
            { id: 13, dutch: "Zij heeft haar ___ behaald.", english: "She obtained her diploma.", answer: "diploma", difficulty: "intermediate", audio: "Zij heeft haar diploma behaald" },
            { id: 14, dutch: "Het ___ systeem is complex.", english: "The political system is complex.", answer: "politieke", difficulty: "advanced", audio: "Het politieke systeem is complex" },
            { id: 15, dutch: "De ___ heeft veel invloed.", english: "The government has much influence.", answer: "regering", difficulty: "advanced", audio: "De regering heeft veel invloed" }
        ];

        // App state
        let currentSentence = null;
        let score = parseInt(localStorage.getItem('score') || '0');
        let totalAnswered = parseInt(localStorage.getItem('totalAnswered') || '0');
        let streak = parseInt(localStorage.getItem('streak') || '0');
        let currentDifficulty = 'common';
        let showingAnswer = false;

        // DOM elements
        const scoreEl = document.getElementById('score');
        const totalEl = document.getElementById('total');
        const accuracyEl = document.getElementById('accuracy');
        const streakEl = document.getElementById('streak');
        const dutchSentenceEl = document.getElementById('dutch-sentence');
        const englishSentenceEl = document.getElementById('english-sentence');
        const answerInputEl = document.getElementById('answer-input');
        const feedbackEl = document.getElementById('feedback');
        const feedbackTextEl = document.getElementById('feedback-text');
        const correctAnswerEl = document.getElementById('correct-answer');
        const checkBtnEl = document.getElementById('check-btn');
        const nextBtnEl = document.getElementById('next-btn');
        const speakerBtnEl = document.getElementById('speaker-btn');
        const resetBtnEl = document.getElementById('reset-btn');

        // Initialize app
        function init() {
            updateStats();
            loadNextSentence();
            setupEventListeners();
            
            // Register service worker for PWA
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('sw.js');
            }
        }

        function setupEventListeners() {
            // Difficulty buttons
            document.querySelectorAll('.difficulty-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    document.querySelector('.difficulty-btn.active').classList.remove('active');
                    e.target.classList.add('active');
                    currentDifficulty = e.target.dataset.difficulty;
                    loadNextSentence();
                });
            });

            // Check answer button
            checkBtnEl.addEventListener('click', checkAnswer);

            // Next sentence button
            nextBtnEl.addEventListener('click', loadNextSentence);

            // Speaker button
            speakerBtnEl.addEventListener('click', speakSentence);

            // Reset button
            resetBtnEl.addEventListener('click', resetProgress);

            // Enter key to check answer
            answerInputEl.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !showingAnswer) {
                    checkAnswer();
                }
            });
        }

        function updateStats() {
            scoreEl.textContent = score;
            totalEl.textContent = totalAnswered;
            accuracyEl.textContent = totalAnswered > 0 ? Math.round((score / totalAnswered) * 100) : 0;
            streakEl.textContent = streak;
        }

        function loadNextSentence() {
            const filteredSentences = sentences.filter(s => s.difficulty === currentDifficulty);
            const randomIndex = Math.floor(Math.random() * filteredSentences.length);
            currentSentence = filteredSentences[randomIndex];
            
            dutchSentenceEl.textContent = currentSentence.dutch;
            englishSentenceEl.textContent = currentSentence.english;
            answerInputEl.value = '';
            answerInputEl.className = 'answer-input';
            answerInputEl.disabled = false;
            
            feedbackEl.classList.add('hidden');
            checkBtnEl.classList.remove('hidden');
            nextBtnEl.classList.add('hidden');
            showingAnswer = false;
            
            answerInputEl.focus();
        }

        function checkAnswer() {
            const userAnswer = answerInputEl.value.trim();
            if (!userAnswer) {
                alert('Please enter an answer');
                return;
            }

            const isCorrect = userAnswer.toLowerCase() === currentSentence.answer.toLowerCase();
            showingAnswer = true;
            
            // Update stats
            totalAnswered++;
            if (isCorrect) {
                score++;
                streak++;
            } else {
                streak = 0;
            }
            
            // Save progress
            localStorage.setItem('score', score.toString());
            localStorage.setItem('totalAnswered', totalAnswered.toString());
            localStorage.setItem('streak', streak.toString());
            
            // Update UI
            updateStats();
            answerInputEl.className = `answer-input ${isCorrect ? 'correct' : 'incorrect'}`;
            answerInputEl.disabled = true;
            
            feedbackTextEl.textContent = isCorrect ? '✅ Correct!' : '❌ Incorrect';
            feedbackTextEl.className = `feedback-text ${isCorrect ? 'correct' : 'incorrect'}`;
            
            if (!isCorrect) {
                correctAnswerEl.textContent = `Correct answer: ${currentSentence.answer}`;
                correctAnswerEl.classList.remove('hidden');
            } else {
                correctAnswerEl.classList.add('hidden');
            }
            
            feedbackEl.classList.remove('hidden');
            checkBtnEl.classList.add('hidden');
            nextBtnEl.classList.remove('hidden');
        }

        function speakSentence() {
            if (currentSentence && 'speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(currentSentence.audio);
                utterance.lang = 'nl-NL';
                utterance.rate = 0.8;
                speechSynthesis.speak(utterance);
            }
        }

        function resetProgress() {
            if (confirm('Are you sure you want to reset all progress?')) {
                score = 0;
                totalAnswered = 0;
                streak = 0;
                localStorage.removeItem('score');
                localStorage.removeItem('totalAnswered');
                localStorage.removeItem('streak');
                updateStats();
            }
        }

        // Initialize app when DOM is loaded
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Dutch Learning - Clozemaster <PERSON></title>
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#2196F3">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Dutch Learning">
    <link rel="apple-touch-icon" href="icon-192.png">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            color: #333;
            line-height: 1.6;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Glass morphism base styles */
        .glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }

        .glass-dark {
            background: rgba(0, 0, 0, 0.25);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
        }

        /* Smooth animations */
        * {
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }

        /* Floating animation */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* Pulse animation */
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* Shimmer effect */
        @keyframes shimmer {
            0% { background-position: -200px 0; }
            100% { background-position: calc(200px + 100%) 0; }
        }

        .shimmer {
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            background-size: 200px 100%;
            animation: shimmer 2s infinite;
        }
        
        .container {
            max-width: 100vw;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .header {
            background: rgba(33, 150, 243, 0.3);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border-radius: 0 0 25px 25px;
            animation: float 6s ease-in-out infinite;
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            letter-spacing: -0.5px;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            font-size: 14px;
            font-weight: 500;
        }

        .difficulty-selector {
            display: flex;
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            margin: 15px;
            border-radius: 20px;
            padding: 8px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        .difficulty-btn {
            flex: 1;
            padding: 12px 16px;
            text-align: center;
            border: none;
            background: transparent;
            border-radius: 15px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.8);
            position: relative;
            overflow: hidden;
        }

        .difficulty-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .difficulty-btn.active {
            background: rgba(33, 150, 243, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: white;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
            transform: scale(1.05);
        }

        .difficulty-btn.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }
        
        .exercise-card {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            margin: 15px;
            padding: 30px;
            border-radius: 25px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            flex: 1;
            position: relative;
            overflow: hidden;
            animation: float 8s ease-in-out infinite;
        }

        .exercise-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
        }
        
        .sentence-container {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            gap: 15px;
        }

        .sentence-actions {
            display: flex;
            gap: 10px;
        }
        
        .dutch-sentence {
            font-size: 22px;
            font-weight: 700;
            flex: 1;
            line-height: 1.4;
            color: rgba(255, 255, 255, 0.95);
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            letter-spacing: -0.3px;
        }

        .speaker-btn {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            font-size: 24px;
            cursor: pointer;
            padding: 12px;
            border-radius: 50%;
            color: rgba(255, 255, 255, 0.9);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .speaker-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .speaker-btn:active {
            transform: scale(0.95);
            animation: pulse 0.6s ease-in-out;
        }

        .translate-btn {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            font-size: 20px;
            cursor: pointer;
            padding: 12px;
            border-radius: 50%;
            color: rgba(255, 255, 255, 0.9);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .translate-btn:hover {
            background: rgba(76, 175, 80, 0.3);
            transform: scale(1.1) rotate(-5deg);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
        }

        .translate-btn:active {
            transform: scale(0.95);
            animation: pulse 0.6s ease-in-out;
        }

        .translate-btn.active {
            background: rgba(76, 175, 80, 0.8);
            color: white;
        }

        .keyboard-hint {
            position: absolute;
            bottom: -8px;
            right: -8px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            font-size: 10px;
            font-weight: bold;
            padding: 2px 4px;
            border-radius: 4px;
            opacity: 0.7;
            pointer-events: none;
            font-family: monospace;
        }

        .speaker-btn, .translate-btn {
            position: relative;
        }
        
        .english-sentence {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
            font-style: italic;
            margin-bottom: 25px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            font-weight: 500;
        }

        .answer-input {
            width: 100%;
            padding: 18px 20px;
            font-size: 18px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            margin-bottom: 20px;
            color: white;
            font-weight: 500;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .answer-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
            font-style: italic;
        }

        .answer-input:focus {
            outline: none;
            border-color: rgba(33, 150, 243, 0.8);
            background: rgba(255, 255, 255, 0.25);
            box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.2), inset 0 2px 4px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .answer-input.correct {
            border-color: rgba(76, 175, 80, 0.8);
            background: rgba(76, 175, 80, 0.2);
            box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.3), inset 0 2px 4px rgba(0,0,0,0.1);
            animation: pulse 0.6s ease-in-out;
        }

        .answer-input.incorrect {
            border-color: rgba(244, 67, 54, 0.8);
            background: rgba(244, 67, 54, 0.2);
            box-shadow: 0 0 0 4px rgba(244, 67, 54, 0.3), inset 0 2px 4px rgba(0,0,0,0.1);
            animation: shake 0.6s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes celebrationPop {
            0% {
                transform: translate(-50%, -50%) scale(0) rotate(0deg);
                opacity: 0;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) scale(0.8) rotate(360deg);
                opacity: 0;
            }
        }
        
        .feedback {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .feedback-text {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .feedback-text.correct {
            color: #4CAF50;
        }
        
        .feedback-text.incorrect {
            color: #f44336;
        }
        
        .correct-answer {
            font-size: 16px;
            color: #666;
            font-style: italic;
        }
        
        .button-container {
            text-align: center;
        }
        
        .btn {
            padding: 18px 35px;
            font-size: 16px;
            font-weight: 700;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            min-width: 160px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-check {
            background: rgba(33, 150, 243, 0.8);
            color: white;
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
        }

        .btn-next {
            background: rgba(76, 175, 80, 0.8);
            color: white;
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
        }

        .btn:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 12px 35px rgba(0,0,0,0.3);
        }

        .btn:active {
            transform: translateY(-1px) scale(0.98);
        }
        
        .footer {
            padding: 20px;
            text-align: center;
        }

        .keyboard-shortcuts {
            margin-bottom: 15px;
        }

        .shortcut-hint {
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            background: rgba(0, 0, 0, 0.3);
            padding: 8px 12px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        kbd {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 11px;
            font-weight: bold;
            margin: 0 2px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }

        .reset-btn {
            background: none;
            border: none;
            color: #f44336;
            cursor: pointer;
            font-size: 14px;
            padding: 10px;
        }
        
        .hidden {
            display: none;
        }
        
        /* iOS specific styles */
        @supports (-webkit-touch-callout: none) {
            .container {
                padding-bottom: env(safe-area-inset-bottom);
            }
            
            .header {
                padding-top: max(20px, env(safe-area-inset-top));
            }
        }
        
        /* Responsive design */
        @media (max-width: 480px) {
            .exercise-card {
                margin: 10px;
                padding: 20px;
            }
            
            .dutch-sentence {
                font-size: 18px;
            }
            
            .answer-input {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">Dutch Learning</div>
            <div class="stats">
                <span>Score: <span id="score">0</span>/<span id="total">0</span></span>
                <span>Accuracy: <span id="accuracy">0</span>%</span>
                <span>Streak: <span id="streak">0</span></span>
            </div>
        </div>
        
        <div class="difficulty-selector">
            <button class="difficulty-btn active" data-difficulty="common">Common</button>
            <button class="difficulty-btn" data-difficulty="intermediate">Intermediate</button>
            <button class="difficulty-btn" data-difficulty="advanced">Advanced</button>
        </div>
        
        <div class="exercise-card">
            <div class="sentence-container">
                <div class="dutch-sentence" id="dutch-sentence">Loading...</div>
                <div class="sentence-actions">
                    <button class="speaker-btn" id="speaker-btn" title="Press P for pronunciation">
                        🔊
                        <span class="keyboard-hint">P</span>
                    </button>
                    <button class="translate-btn" id="translate-btn" title="Press L for translation">
                        🌐
                        <span class="keyboard-hint">L</span>
                    </button>
                </div>
            </div>
            
            <div class="english-sentence" id="english-sentence">Loading...</div>
            
            <input type="text" class="answer-input" id="answer-input" placeholder="Type your answer here... (Enter to submit)" autocomplete="off" autocapitalize="none" autocorrect="off">
            
            <div class="feedback hidden" id="feedback">
                <div class="feedback-text" id="feedback-text"></div>
                <div class="correct-answer hidden" id="correct-answer"></div>
            </div>
            
            <div class="button-container">
                <button class="btn btn-check" id="check-btn">Check Answer</button>
                <button class="btn btn-next hidden" id="next-btn">Next Sentence</button>
            </div>
        </div>
        
        <div class="footer">
            <div class="keyboard-shortcuts">
                <span class="shortcut-hint">⌨️ Shortcuts: <kbd>P</kbd> = Pronunciation, <kbd>L</kbd> = Language, <kbd>Enter</kbd> = Submit/Next</span>
            </div>
            <button class="reset-btn" id="reset-btn">Reset Progress</button>
        </div>
    </div>

    <script>
        // Dutch sentences data
        const sentences = [
            // Common Level - Basic everyday vocabulary
            { id: 1, dutch: "Ik hou van ___.", english: "I love coffee.", answer: "koffie", difficulty: "common", audio: "Ik hou van koffie" },
            { id: 2, dutch: "Het ___ is mooi vandaag.", english: "The weather is beautiful today.", answer: "weer", difficulty: "common", audio: "Het weer is mooi vandaag" },
            { id: 3, dutch: "Waar is de ___?", english: "Where is the bathroom?", answer: "badkamer", difficulty: "common", audio: "Waar is de badkamer" },
            { id: 4, dutch: "Ik wil graag een ___ bestellen.", english: "I would like to order a beer.", answer: "biertje", difficulty: "common", audio: "Ik wil graag een biertje bestellen" },
            { id: 5, dutch: "Hoeveel ___ dit?", english: "How much does this cost?", answer: "kost", difficulty: "common", audio: "Hoeveel kost dit" },
            { id: 6, dutch: "Ik spreek geen ___.", english: "I don't speak Dutch.", answer: "Nederlands", difficulty: "common", audio: "Ik spreek geen Nederlands" },
            { id: 7, dutch: "Kun je me ___?", english: "Can you help me?", answer: "helpen", difficulty: "common", audio: "Kun je me helpen" },
            { id: 8, dutch: "Ik ben ___ aan het leren.", english: "I am learning Dutch.", answer: "Nederlands", difficulty: "common", audio: "Ik ben Nederlands aan het leren" },
            { id: 9, dutch: "De ___ is gesloten.", english: "The store is closed.", answer: "winkel", difficulty: "common", audio: "De winkel is gesloten" },
            { id: 10, dutch: "Wat is je ___?", english: "What is your name?", answer: "naam", difficulty: "common", audio: "Wat is je naam" },
            { id: 16, dutch: "Ik ga naar de ___ om boodschappen te doen.", english: "I'm going to the supermarket to do groceries.", answer: "supermarkt", difficulty: "common", audio: "Ik ga naar de supermarkt om boodschappen te doen" },
            { id: 17, dutch: "De ___ vertrekt om acht uur.", english: "The train departs at eight o'clock.", answer: "trein", difficulty: "common", audio: "De trein vertrekt om acht uur" },
            { id: 18, dutch: "Mijn ___ woont in Amsterdam.", english: "My brother lives in Amsterdam.", answer: "broer", difficulty: "common", audio: "Mijn broer woont in Amsterdam" },
            { id: 19, dutch: "Het ___ is vandaag gesloten.", english: "The museum is closed today.", answer: "museum", difficulty: "common", audio: "Het museum is vandaag gesloten" },
            { id: 20, dutch: "Ik heb ___ nodig voor mijn werk.", english: "I need a computer for my work.", answer: "computer", difficulty: "common", audio: "Ik heb een computer nodig voor mijn werk" },
            { id: 61, dutch: "De ___ is vandaag open tot zes uur.", english: "The bank is open until six o'clock today.", answer: "bank", difficulty: "common", audio: "De bank is vandaag open tot zes uur" },
            { id: 62, dutch: "Mijn ___ werkt in het ziekenhuis.", english: "My father works in the hospital.", answer: "vader", difficulty: "common", audio: "Mijn vader werkt in het ziekenhuis" },
            { id: 63, dutch: "Het ___ begint om acht uur.", english: "The concert starts at eight o'clock.", answer: "concert", difficulty: "common", audio: "Het concert begint om acht uur" },
            { id: 64, dutch: "Ik ga naar de ___ om fruit te kopen.", english: "I'm going to the market to buy fruit.", answer: "markt", difficulty: "common", audio: "Ik ga naar de markt om fruit te kopen" },
            { id: 65, dutch: "De ___ rijdt elke tien minuten.", english: "The bus runs every ten minutes.", answer: "bus", difficulty: "common", audio: "De bus rijdt elke tien minuten" },
            { id: 66, dutch: "Ik drink graag ___ in de ochtend.", english: "I like to drink tea in the morning.", answer: "thee", difficulty: "common", audio: "Ik drink graag thee in de ochtend" },
            { id: 67, dutch: "De ___ ligt op de tafel.", english: "The book is on the table.", answer: "boek", difficulty: "common", audio: "Het boek ligt op de tafel" },
            { id: 68, dutch: "Mijn ___ is heel aardig.", english: "My teacher is very nice.", answer: "leraar", difficulty: "common", audio: "Mijn leraar is heel aardig" },
            { id: 69, dutch: "Het ___ is koud vandaag.", english: "The water is cold today.", answer: "water", difficulty: "common", audio: "Het water is koud vandaag" },
            { id: 70, dutch: "Ik woon in een kleine ___.", english: "I live in a small city.", answer: "stad", difficulty: "common", audio: "Ik woon in een kleine stad" },
            { id: 71, dutch: "De ___ smaakt heerlijk.", english: "The pizza tastes delicious.", answer: "pizza", difficulty: "common", audio: "De pizza smaakt heerlijk" },
            { id: 72, dutch: "Mijn ___ is blauw.", english: "My car is blue.", answer: "auto", difficulty: "common", audio: "Mijn auto is blauw" },
            { id: 73, dutch: "Het ___ is heel interessant.", english: "The movie is very interesting.", answer: "film", difficulty: "common", audio: "De film is heel interessant" },
            { id: 74, dutch: "Ik ga naar ___ om te studeren.", english: "I'm going to school to study.", answer: "school", difficulty: "common", audio: "Ik ga naar school om te studeren" },
            { id: 75, dutch: "De ___ is heel mooi.", english: "The flower is very beautiful.", answer: "bloem", difficulty: "common", audio: "De bloem is heel mooi" },


            // Intermediate Level - More complex vocabulary and grammar
            { id: 11, dutch: "Hij heeft een ___ gekocht.", english: "He bought a bicycle.", answer: "fiets", difficulty: "intermediate", audio: "Hij heeft een fiets gekocht" },
            { id: 12, dutch: "De vergadering is ___.", english: "The meeting is postponed.", answer: "uitgesteld", difficulty: "intermediate", audio: "De vergadering is uitgesteld" },
            { id: 13, dutch: "Zij heeft haar ___ behaald.", english: "She obtained her diploma.", answer: "diploma", difficulty: "intermediate", audio: "Zij heeft haar diploma behaald" },
            { id: 21, dutch: "De ___ van dit probleem is ingewikkeld.", english: "The solution to this problem is complicated.", answer: "oplossing", difficulty: "intermediate", audio: "De oplossing van dit probleem is ingewikkeld" },
            { id: 22, dutch: "Ik moet mijn ___ verlengen voor de reis.", english: "I need to renew my passport for the trip.", answer: "paspoort", difficulty: "intermediate", audio: "Ik moet mijn paspoort verlengen voor de reis" },
            { id: 23, dutch: "De ___ heeft besloten om de wet te wijzigen.", english: "The government has decided to change the law.", answer: "regering", difficulty: "intermediate", audio: "De regering heeft besloten om de wet te wijzigen" },
            { id: 24, dutch: "Hij werkt als ___ in het ziekenhuis.", english: "He works as a doctor in the hospital.", answer: "dokter", difficulty: "intermediate", audio: "Hij werkt als dokter in het ziekenhuis" },
            { id: 25, dutch: "De ___ van de universiteit is zeer streng.", english: "The entrance exam of the university is very strict.", answer: "toelatingsexamen", difficulty: "intermediate", audio: "Het toelatingsexamen van de universiteit is zeer streng" },
            { id: 26, dutch: "Wij hebben een ___ nodig voor dit project.", english: "We need a specialist for this project.", answer: "specialist", difficulty: "intermediate", audio: "Wij hebben een specialist nodig voor dit project" },
            { id: 27, dutch: "De ___ van het bedrijf is gestegen.", english: "The profit of the company has increased.", answer: "winst", difficulty: "intermediate", audio: "De winst van het bedrijf is gestegen" },
            { id: 28, dutch: "Zij heeft een ___ gemaakt in haar presentatie.", english: "She made a mistake in her presentation.", answer: "fout", difficulty: "intermediate", audio: "Zij heeft een fout gemaakt in haar presentatie" },
            { id: 29, dutch: "Het ___ van dit medicijn is nog niet bekend.", english: "The side effect of this medicine is not yet known.", answer: "bijeffect", difficulty: "intermediate", audio: "Het bijeffect van dit medicijn is nog niet bekend" },
            { id: 30, dutch: "De ___ heeft de nieuwe regels uitgelegd.", english: "The manager has explained the new rules.", answer: "manager", difficulty: "intermediate", audio: "De manager heeft de nieuwe regels uitgelegd" },
            { id: 51, dutch: "Het ___ van de film was zeer spannend.", english: "The plot of the movie was very exciting.", answer: "verhaal", difficulty: "intermediate", audio: "Het verhaal van de film was zeer spannend" },
            { id: 52, dutch: "De ___ van dit product is uitstekend.", english: "The quality of this product is excellent.", answer: "kwaliteit", difficulty: "intermediate", audio: "De kwaliteit van dit product is uitstekend" },
            { id: 76, dutch: "De ___ heeft een nieuwe wet aangenomen.", english: "The parliament has adopted a new law.", answer: "parlement", difficulty: "intermediate", audio: "Het parlement heeft een nieuwe wet aangenomen" },
            { id: 77, dutch: "Zijn ___ over dit onderwerp is zeer interessant.", english: "His opinion about this subject is very interesting.", answer: "mening", difficulty: "intermediate", audio: "Zijn mening over dit onderwerp is zeer interessant" },
            { id: 78, dutch: "De ___ van de stad is indrukwekkend.", english: "The history of the city is impressive.", answer: "geschiedenis", difficulty: "intermediate", audio: "De geschiedenis van de stad is indrukwekkend" },
            { id: 79, dutch: "Het ___ van dit bedrijf is uitstekend.", english: "The management of this company is excellent.", answer: "management", difficulty: "intermediate", audio: "Het management van dit bedrijf is uitstekend" },
            { id: 80, dutch: "De ___ heeft veel investeringen aangetrokken.", english: "The project has attracted many investments.", answer: "project", difficulty: "intermediate", audio: "Het project heeft veel investeringen aangetrokken" },
            { id: 81, dutch: "Zijn ___ in de technologie is zeer waardevol.", english: "His knowledge in technology is very valuable.", answer: "kennis", difficulty: "intermediate", audio: "Zijn kennis in de technologie is zeer waardevol" },
            { id: 82, dutch: "De ___ van het probleem is complex.", english: "The nature of the problem is complex.", answer: "aard", difficulty: "intermediate", audio: "De aard van het probleem is complex" },
            { id: 83, dutch: "Het ___ van de organisatie is veranderd.", english: "The structure of the organization has changed.", answer: "structuur", difficulty: "intermediate", audio: "De structuur van de organisatie is veranderd" },
            { id: 84, dutch: "De ___ tussen beide partijen is verbeterd.", english: "The communication between both parties has improved.", answer: "communicatie", difficulty: "intermediate", audio: "De communicatie tussen beide partijen is verbeterd" },
            { id: 85, dutch: "Zijn ___ tot het probleem was effectief.", english: "His approach to the problem was effective.", answer: "aanpak", difficulty: "intermediate", audio: "Zijn aanpak tot het probleem was effectief" },

            // Advanced Level - Complex academic and professional vocabulary
            { id: 14, dutch: "Het ___ systeem is complex.", english: "The political system is complex.", answer: "politieke", difficulty: "advanced", audio: "Het politieke systeem is complex" },
            { id: 15, dutch: "De ___ heeft veel invloed.", english: "The government has much influence.", answer: "regering", difficulty: "advanced", audio: "De regering heeft veel invloed" },
            { id: 31, dutch: "De ___ van deze theorie is nog niet bewezen.", english: "The validity of this theory has not yet been proven.", answer: "geldigheid", difficulty: "advanced", audio: "De geldigheid van deze theorie is nog niet bewezen" },
            { id: 32, dutch: "Hij heeft een ___ bijdrage geleverd aan de wetenschap.", english: "He has made a significant contribution to science.", answer: "aanzienlijke", difficulty: "advanced", audio: "Hij heeft een aanzienlijke bijdrage geleverd aan de wetenschap" },
            { id: 33, dutch: "De ___ van het klimaat heeft ernstige gevolgen.", english: "Climate change has serious consequences.", answer: "verandering", difficulty: "advanced", audio: "De klimaatverandering heeft ernstige gevolgen" },
            { id: 34, dutch: "Deze ___ vereist een grondige analyse.", english: "This phenomenon requires a thorough analysis.", answer: "verschijnsel", difficulty: "advanced", audio: "Dit verschijnsel vereist een grondige analyse" },
            { id: 35, dutch: "De ___ tussen beide landen is verslechterd.", english: "The relationship between both countries has deteriorated.", answer: "verhouding", difficulty: "advanced", audio: "De verhouding tussen beide landen is verslechterd" },
            { id: 36, dutch: "Zijn ___ over dit onderwerp is zeer controversieel.", english: "His opinion on this subject is very controversial.", answer: "standpunt", difficulty: "advanced", audio: "Zijn standpunt over dit onderwerp is zeer controversieel" },
            { id: 37, dutch: "De ___ van de economie is onvoorspelbaar.", english: "The development of the economy is unpredictable.", answer: "ontwikkeling", difficulty: "advanced", audio: "De ontwikkeling van de economie is onvoorspelbaar" },
            { id: 38, dutch: "Deze ___ heeft internationale aandacht getrokken.", english: "This crisis has attracted international attention.", answer: "crisis", difficulty: "advanced", audio: "Deze crisis heeft internationale aandacht getrokken" },
            { id: 39, dutch: "De ___ van dit medicijn is nog in onderzoek.", english: "The effectiveness of this medicine is still under research.", answer: "doeltreffendheid", difficulty: "advanced", audio: "De doeltreffendheid van dit medicijn is nog in onderzoek" },
            { id: 40, dutch: "Zijn ___ in de filosofie is wereldwijd erkend.", english: "His expertise in philosophy is recognized worldwide.", answer: "deskundigheid", difficulty: "advanced", audio: "Zijn deskundigheid in de filosofie is wereldwijd erkend" },
            { id: 41, dutch: "De ___ van deze beslissing zal jaren duren.", english: "The implementation of this decision will take years.", answer: "uitvoering", difficulty: "advanced", audio: "De uitvoering van deze beslissing zal jaren duren" },
            { id: 42, dutch: "Deze ___ heeft de hele industrie veranderd.", english: "This innovation has changed the entire industry.", answer: "innovatie", difficulty: "advanced", audio: "Deze innovatie heeft de hele industrie veranderd" },
            { id: 43, dutch: "De ___ van dit kunstwerk is onschatbaar.", english: "The value of this artwork is invaluable.", answer: "waarde", difficulty: "advanced", audio: "De waarde van dit kunstwerk is onschatbaar" },
            { id: 86, dutch: "De ___ van deze wetenschappelijke theorie is omstreden.", english: "The validity of this scientific theory is disputed.", answer: "geldigheid", difficulty: "advanced", audio: "De geldigheid van deze wetenschappelijke theorie is omstreden" },
            { id: 87, dutch: "Het ___ van dit fenomeen vereist verder onderzoek.", english: "The mechanism of this phenomenon requires further research.", answer: "mechanisme", difficulty: "advanced", audio: "Het mechanisme van dit fenomeen vereist verder onderzoek" },
            { id: 88, dutch: "De ___ tussen oorzaak en gevolg is duidelijk.", english: "The correlation between cause and effect is clear.", answer: "correlatie", difficulty: "advanced", audio: "De correlatie tussen oorzaak en gevolg is duidelijk" },
            { id: 89, dutch: "Zijn ___ van de situatie was accuraat.", english: "His assessment of the situation was accurate.", answer: "beoordeling", difficulty: "advanced", audio: "Zijn beoordeling van de situatie was accuraat" },
            { id: 90, dutch: "De ___ van dit algoritme is indrukwekkend.", english: "The efficiency of this algorithm is impressive.", answer: "efficiëntie", difficulty: "advanced", audio: "De efficiëntie van dit algoritme is indrukwekkend" },
            { id: 91, dutch: "Het ___ van deze hypothese moet worden getest.", english: "The validity of this hypothesis must be tested.", answer: "geldigheid", difficulty: "advanced", audio: "De geldigheid van deze hypothese moet worden getest" },
            { id: 92, dutch: "De ___ van dit systeem is zeer complex.", english: "The architecture of this system is very complex.", answer: "architectuur", difficulty: "advanced", audio: "De architectuur van dit systeem is zeer complex" },
            { id: 93, dutch: "Zijn ___ in dit vakgebied is onbetwist.", english: "His expertise in this field is undisputed.", answer: "expertise", difficulty: "advanced", audio: "Zijn expertise in dit vakgebied is onbetwist" },
            { id: 94, dutch: "De ___ van deze methode is bewezen.", english: "The effectiveness of this method is proven.", answer: "doeltreffendheid", difficulty: "advanced", audio: "De doeltreffendheid van deze methode is bewezen" },
            { id: 95, dutch: "Het ___ van dit onderzoek is significant.", english: "The scope of this research is significant.", answer: "bereik", difficulty: "advanced", audio: "Het bereik van dit onderzoek is significant" },
            { id: 44, dutch: "Zijn ___ tot dit resultaat was opmerkelijk.", english: "His approach to this result was remarkable.", answer: "benadering", difficulty: "advanced", audio: "Zijn benadering tot dit resultaat was opmerkelijk" },
            { id: 45, dutch: "De ___ van deze technologie is revolutionair.", english: "The application of this technology is revolutionary.", answer: "toepassing", difficulty: "advanced", audio: "De toepassing van deze technologie is revolutionair" },

        ];

        // App state
        let currentSentence = null;
        let score = parseInt(localStorage.getItem('score') || '0');
        let totalAnswered = parseInt(localStorage.getItem('totalAnswered') || '0');
        let streak = parseInt(localStorage.getItem('streak') || '0');
        let currentDifficulty = 'common';
        let showingAnswer = false;
        let translationLanguage = localStorage.getItem('translationLanguage') || 'en';
        let isTranslating = false;

        // DOM elements
        const scoreEl = document.getElementById('score');
        const totalEl = document.getElementById('total');
        const accuracyEl = document.getElementById('accuracy');
        const streakEl = document.getElementById('streak');
        const dutchSentenceEl = document.getElementById('dutch-sentence');
        const englishSentenceEl = document.getElementById('english-sentence');
        const answerInputEl = document.getElementById('answer-input');
        const feedbackEl = document.getElementById('feedback');
        const feedbackTextEl = document.getElementById('feedback-text');
        const correctAnswerEl = document.getElementById('correct-answer');
        const checkBtnEl = document.getElementById('check-btn');
        const nextBtnEl = document.getElementById('next-btn');
        const speakerBtnEl = document.getElementById('speaker-btn');
        const translateBtnEl = document.getElementById('translate-btn');
        const resetBtnEl = document.getElementById('reset-btn');

        // Initialize app
        function init() {
            updateStats();
            loadNextSentence();
            setupEventListeners();
            
            // Register service worker for PWA
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('sw.js');
            }
        }

        function setupEventListeners() {
            // Difficulty buttons
            document.querySelectorAll('.difficulty-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    document.querySelector('.difficulty-btn.active').classList.remove('active');
                    e.target.classList.add('active');
                    currentDifficulty = e.target.dataset.difficulty;
                    loadNextSentence();
                });
            });

            // Check answer button
            checkBtnEl.addEventListener('click', checkAnswer);

            // Next sentence button
            nextBtnEl.addEventListener('click', loadNextSentence);

            // Speaker button
            speakerBtnEl.addEventListener('click', speakSentence);

            // Translate button
            translateBtnEl.addEventListener('click', toggleTranslation);

            // Reset button
            resetBtnEl.addEventListener('click', resetProgress);

            // Enhanced Enter key handling - ALWAYS WORKS!
            const handleEnterKey = (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    e.stopPropagation();

                    if (!showingAnswer) {
                        // Check the answer
                        checkAnswer();
                    } else {
                        // Go to next sentence
                        loadNextSentence();
                    }
                }
            };

            // Multiple event listeners for maximum compatibility
            answerInputEl.addEventListener('keypress', handleEnterKey);
            answerInputEl.addEventListener('keydown', handleEnterKey);
            answerInputEl.addEventListener('keyup', handleEnterKey);

            // Global keyboard shortcuts
            document.addEventListener('keydown', (e) => {
                // P key for pronunciation (speaker)
                if (e.key.toLowerCase() === 'p' && e.target !== answerInputEl) {
                    e.preventDefault();
                    speakSentence();
                    // Add visual feedback
                    speakerBtnEl.style.transform = 'scale(0.9)';
                    setTimeout(() => {
                        speakerBtnEl.style.transform = '';
                    }, 150);
                }

                // L key for language translation
                if (e.key.toLowerCase() === 'l' && e.target !== answerInputEl) {
                    e.preventDefault();
                    toggleTranslation();
                    // Add visual feedback
                    translateBtnEl.style.transform = 'scale(0.9)';
                    setTimeout(() => {
                        translateBtnEl.style.transform = '';
                    }, 150);
                }

                // Enter key handling
                if (e.key === 'Enter') {
                    e.preventDefault();
                    handleEnterKey(e);
                }
            });

            // Also handle form submission if wrapped in form
            document.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && e.target === answerInputEl) {
                    e.preventDefault();
                    handleEnterKey(e);
                }
            });
        }

        function updateStats() {
            scoreEl.textContent = score;
            totalEl.textContent = totalAnswered;
            accuracyEl.textContent = totalAnswered > 0 ? Math.round((score / totalAnswered) * 100) : 0;
            streakEl.textContent = streak;
        }

        function loadNextSentence() {
            // Add smooth transition effect
            const exerciseCard = document.querySelector('.exercise-card');
            exerciseCard.style.opacity = '0.7';
            exerciseCard.style.transform = 'scale(0.95)';

            setTimeout(() => {
                const filteredSentences = sentences.filter(s => s.difficulty === currentDifficulty);
                const randomIndex = Math.floor(Math.random() * filteredSentences.length);
                currentSentence = filteredSentences[randomIndex];

                dutchSentenceEl.textContent = currentSentence.dutch;
                englishSentenceEl.textContent = currentSentence.english;
                answerInputEl.value = '';
                answerInputEl.className = 'answer-input';
                answerInputEl.disabled = false;

                feedbackEl.classList.add('hidden');
                checkBtnEl.classList.remove('hidden');
                nextBtnEl.classList.add('hidden');
                showingAnswer = false;

                // Restore card appearance with animation
                exerciseCard.style.opacity = '1';
                exerciseCard.style.transform = 'scale(1)';

                // Focus input with slight delay for better UX
                setTimeout(() => {
                    answerInputEl.focus();
                }, 100);
            }, 200);
        }

        function checkAnswer() {
            const userAnswer = answerInputEl.value.trim();
            if (!userAnswer) {
                // Add gentle shake animation for empty input
                answerInputEl.style.animation = 'shake 0.6s ease-in-out';
                setTimeout(() => {
                    answerInputEl.style.animation = '';
                }, 600);
                return;
            }

            const isCorrect = userAnswer.toLowerCase() === currentSentence.answer.toLowerCase();
            showingAnswer = true;

            // Update stats
            totalAnswered++;
            if (isCorrect) {
                score++;
                streak++;
            } else {
                streak = 0;
            }

            // Save progress
            localStorage.setItem('score', score.toString());
            localStorage.setItem('totalAnswered', totalAnswered.toString());
            localStorage.setItem('streak', streak.toString());

            // Update UI with smooth animations
            updateStats();
            answerInputEl.className = `answer-input ${isCorrect ? 'correct' : 'incorrect'}`;
            answerInputEl.disabled = true;

            // Add celebration effect for correct answers
            if (isCorrect) {
                createCelebrationEffect();
            }

            feedbackTextEl.textContent = isCorrect ? '✅ Correct!' : '❌ Incorrect';
            feedbackTextEl.className = `feedback-text ${isCorrect ? 'correct' : 'incorrect'}`;

            if (!isCorrect) {
                correctAnswerEl.textContent = `Correct answer: ${currentSentence.answer}`;
                correctAnswerEl.classList.remove('hidden');
            } else {
                correctAnswerEl.classList.add('hidden');
            }

            // Smooth reveal of feedback
            feedbackEl.style.opacity = '0';
            feedbackEl.classList.remove('hidden');
            setTimeout(() => {
                feedbackEl.style.opacity = '1';
            }, 100);

            checkBtnEl.classList.add('hidden');
            nextBtnEl.classList.remove('hidden');

            // Auto-focus next button for better keyboard navigation
            setTimeout(() => {
                nextBtnEl.focus();
            }, 500);
        }

        // Add celebration effect for correct answers
        function createCelebrationEffect() {
            const exerciseCard = document.querySelector('.exercise-card');
            const celebration = document.createElement('div');
            celebration.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 60px;
                pointer-events: none;
                z-index: 1000;
                animation: celebrationPop 1s ease-out forwards;
            `;
            celebration.textContent = '🎉';
            exerciseCard.appendChild(celebration);

            setTimeout(() => {
                celebration.remove();
            }, 1000);
        }

        function speakSentence() {
            if (currentSentence && 'speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(currentSentence.audio);
                utterance.lang = 'nl-NL';
                utterance.rate = 0.8;
                speechSynthesis.speak(utterance);
            }
        }

        // Translation functionality using browser's built-in translation
        async function translateText(text, targetLang = 'en') {
            try {
                // Try using the experimental Translation API if available
                if ('translation' in window && window.translation) {
                    const translator = await window.translation.createTranslator({
                        sourceLanguage: 'nl',
                        targetLanguage: targetLang
                    });
                    return await translator.translate(text);
                }

                // Fallback to Google Translate API (requires API key in production)
                const response = await fetch(`https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=nl|${targetLang}`);
                const data = await response.json();
                return data.responseData.translatedText;
            } catch (error) {
                console.log('Translation failed, using fallback');
                // Simple fallback translations for common words
                const fallbackTranslations = {
                    'en': {
                        'koffie': 'coffee',
                        'weer': 'weather',
                        'badkamer': 'bathroom',
                        'biertje': 'beer',
                        'Nederlands': 'Dutch',
                        'helpen': 'help',
                        'winkel': 'store',
                        'naam': 'name'
                    },
                    'uk': {
                        'koffie': 'кава',
                        'weer': 'погода',
                        'badkamer': 'ванна кімната',
                        'biertje': 'пиво',
                        'Nederlands': 'голландська',
                        'helpen': 'допомогти',
                        'winkel': 'магазин',
                        'naam': 'ім\'я'
                    }
                };

                return fallbackTranslations[targetLang]?.[text.toLowerCase()] || text;
            }
        }

        async function toggleTranslation() {
            if (isTranslating) return;

            isTranslating = true;
            translateBtnEl.classList.add('active');

            // Cycle through languages: English -> Ukrainian -> Original
            const languages = ['en', 'uk', 'original'];
            const currentIndex = languages.indexOf(translationLanguage);
            const nextIndex = (currentIndex + 1) % languages.length;
            translationLanguage = languages[nextIndex];

            localStorage.setItem('translationLanguage', translationLanguage);

            if (currentSentence) {
                const englishSentenceEl = document.getElementById('english-sentence');

                if (translationLanguage === 'original') {
                    englishSentenceEl.textContent = currentSentence.english;
                    translateBtnEl.textContent = '🌐';
                } else {
                    translateBtnEl.textContent = '⏳';
                    try {
                        const translatedText = await translateText(currentSentence.english, translationLanguage);
                        englishSentenceEl.textContent = translatedText;
                        translateBtnEl.textContent = translationLanguage === 'en' ? '🇺🇸' : '🇺🇦';
                    } catch (error) {
                        englishSentenceEl.textContent = currentSentence.english;
                        translateBtnEl.textContent = '🌐';
                    }
                }
            }

            setTimeout(() => {
                translateBtnEl.classList.remove('active');
                isTranslating = false;
            }, 500);
        }

        function resetProgress() {
            if (confirm('Are you sure you want to reset all progress?')) {
                score = 0;
                totalAnswered = 0;
                streak = 0;
                localStorage.removeItem('score');
                localStorage.removeItem('totalAnswered');
                localStorage.removeItem('streak');
                updateStats();
            }
        }

        // Initialize app when DOM is loaded
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
    </script>
</body>
</html>
